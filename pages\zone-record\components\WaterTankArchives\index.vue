<template>
  <div class="waterTankArchives">
    <div class="header-info">
      <div class="info-card">
        <div class="label">总数量</div>
        <div class="value">{{ menuData.total_count || 0 }}</div>
      </div>
      <div class="info-card">
        <div class="label">区域类别</div>
        <div class="value">{{ menuData.area_category || '-' }}</div>
      </div>
      <div class="info-card">
        <div class="label">系统编码</div>
        <div class="value">{{ menuData.system_code || '-' }}</div>
      </div>
    </div>

    <div class="tank-list" v-if="menuData.T && menuData.T.length > 0">
      <h3 class="section-title">水箱详细信息</h3>
      <div class="tank-grid">
        <div v-for="(tank, index) in menuData.T" :key="index" class="tank-item">
          <div class="tank-header">
            <span class="tank-number">水箱 #{{ index + 1 }}</span>
          </div>
          <div class="tank-details">
            <div class="detail-row">
              <span class="detail-label">位置:</span>
              <span class="detail-value">{{ tank.location || '-' }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">容量:</span>
              <span class="detail-value">{{ tank.capacity || '-' }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">类型:</span>
              <span class="detail-value">{{ tank.type || '-' }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">材质:</span>
              <span class="detail-value">{{ tank.material || '-' }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">数量:</span>
              <span class="detail-value">{{ tank.quantity || '-' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="no-data">
      <div class="no-data-icon">📊</div>
      <div class="no-data-text">暂无水箱数据</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({ data: { type: Object } })

const menuData = computed(() => {
  if (!props.data) return {}
  const { total_count, area_category, system_code } = props.data
  const locations = props.data.location.split(';')
  const capacitys = props.data.capacity.split(';')
  const types = props.data.type.split(';')
  const materials = props.data.material.split(';')
  const quantitys = props.data.quantity.split(';')
  const T = locations.map((item, index) => ({ location: item, capacity: capacitys[index], type: types[index], material: materials[index], quantity: quantitys[index] }))
  return { T, total_count, area_category, system_code }
})
</script>

<style lang="less" scoped>
.waterTankArchives {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;

  .header-info {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    flex-wrap: wrap;

    .info-card {
      background: white;
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      min-width: 120px;
      text-align: center;

      .label {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
      }

      .value {
        font-size: 18px;
        font-weight: 600;
        color: #1890ff;
      }
    }
  }

  .section-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    border-left: 4px solid #1890ff;
    padding-left: 12px;
  }

  .tank-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;

    .tank-item {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .tank-header {
        background: linear-gradient(135deg, #1890ff, #40a9ff);
        color: white;
        padding: 12px 16px;

        .tank-number {
          font-weight: 600;
          font-size: 14px;
        }
      }

      .tank-details {
        padding: 16px;

        .detail-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .detail-label {
            font-size: 13px;
            color: #666;
            font-weight: 500;
          }

          .detail-value {
            font-size: 13px;
            color: #333;
            font-weight: 600;
            max-width: 60%;
            text-align: right;
            word-break: break-word;
          }
        }
      }
    }
  }

  .no-data {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .no-data-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.6;
    }

    .no-data-text {
      font-size: 16px;
      color: #999;
    }
  }
}

@media (max-width: 768px) {
  .waterTankArchives {
    padding: 16px;

    .header-info {
      .info-card {
        flex: 1;
        min-width: 100px;
      }
    }

    .tank-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
