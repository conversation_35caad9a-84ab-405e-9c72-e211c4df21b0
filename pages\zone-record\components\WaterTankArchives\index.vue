<template>
  <div class="waterTankArchives">
    <div class="info-card water-tank-card" v-if="menuData.T && menuData.T.length">
      <div class="card-header">
        <div class="header-icon">
          <wd-icon name="tools" size="18px" color="#13c2c2"></wd-icon>
        </div>
        <div class="header-title">水箱档案</div>
        <div class="header-extra" v-if="menuData.T && menuData.T.length">
          <span class="extra-count"
            >共 <strong>{{ menuData.total_count || (menuData.T && menuData.T.length) || 0 }}</strong> 个</span
          >
          <span class="extra-chip">区域类别：{{ menuData.area_category || '-' }}</span>
          <span class="extra-chip">系统编码：{{ menuData.system_code || '-' }}</span>
        </div>
      </div>

      <div class="info-grid">
        <div class="info-item tank-item" v-for="(tank, index) in menuData.T" :key="index">
          <div class="item-icon tank-icon">
            <wd-icon name="tools" size="14px" color="#13c2c2"></wd-icon>
          </div>
          <div class="item-content">
            <div class="item-label">水箱 #{{ index + 1 }}</div>
            <div class="item-value tank-location">{{ tank.location || '未填写位置' }}</div>
            <div class="tank-details">
              <div class="detail-item">
                <span class="detail-label">容量</span>
                <span class="detail-value">{{ tank.capacity || '-' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">数量</span>
                <span class="detail-value">{{ tank.quantity || '-' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">类型</span>
                <span class="detail-value">{{ tank.type || '-' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">材质</span>
                <span class="detail-value">{{ tank.material || '-' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="!menuData.T || !menuData.T.length" class="no-data small">
      <div class="no-data-illustration">�</div>
      <div class="no-data-title">暂无水箱档案</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({ data: { type: Object } })

const safeSplit = (val) => (val ?? '').toString().split(';')

const menuData = computed(() => {
  const d = props.data
  if (!d) return {}
  const { total_count, area_category, system_code } = d
  const locations = safeSplit(d.location)
  const capacities = safeSplit(d.capacity)
  const types = safeSplit(d.type)
  const materials = safeSplit(d.material)
  const quantities = safeSplit(d.quantity)
  const T = locations.map((item, index) => ({
    location: item || '-',
    capacity: capacities[index] || '-',
    type: types[index] || '-',
    material: materials[index] || '-',
    quantity: quantities[index] || '-'
  }))
  return { T, total_count, area_category, system_code }
})
</script>

<style lang="less" scoped>
.waterTankArchives {
  padding: 20rpx;

  .info-card.water-tank-card {
    background: #fff;
    border-radius: 20rpx;
    padding: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
    margin-bottom: 20rpx;
  }

  .card-header {
    display: flex;
    align-items: center;
    gap: 12rpx;
    margin-bottom: 20rpx;
    flex-wrap: wrap;

    .header-icon {
      width: 36rpx;
      height: 36rpx;
      border-radius: 8rpx;
      background: rgba(19, 194, 194, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .header-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
    }
    .header-extra {
      margin-left: auto;
      display: flex;
      flex-wrap: wrap;
      gap: 8rpx 12rpx;
      align-items: center;
      font-size: 22rpx;
      color: #666;
      line-height: 1.2;

      .extra-count strong {
        color: #13c2c2;
        font-weight: 700;
      }
      .extra-chip {
        background: #f0f9ff;
        color: #0369a1;
        padding: 4rpx 8rpx;
        border-radius: 12rpx;
        font-size: 20rpx;
      }
    }
  }

  // 信息网格 - 与 detail.vue 一致
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
    gap: 16rpx;
  }

  .info-item.tank-item {
    display: flex;
    align-items: flex-start;
    background: rgba(19, 194, 194, 0.02);
    border-radius: 12rpx;
    padding: 16rpx;
    border: 1rpx solid rgba(19, 194, 194, 0.1);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(19, 194, 194, 0.05);
      border-color: rgba(19, 194, 194, 0.2);
      transform: translateY(-2rpx);
    }
  }

  .item-icon.tank-icon {
    width: 32rpx;
    height: 32rpx;
    border-radius: 8rpx;
    background: rgba(19, 194, 194, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12rpx;
    flex-shrink: 0;
  }

  .item-content {
    flex: 1;
  }

  .item-label {
    font-size: 22rpx;
    color: #666;
    line-height: 1.2;
    margin-bottom: 4rpx;
  }

  .item-value.tank-location {
    font-size: 26rpx;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
    margin-bottom: 12rpx;
  }

  .tank-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8rpx 16rpx;
  }

  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6rpx 0;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }
  }

  .detail-label {
    font-size: 20rpx;
    color: #999;
    min-width: 60rpx;
  }

  .detail-value {
    font-size: 22rpx;
    color: #333;
    font-weight: 500;
    text-align: right;
    flex: 1;
  }

  // 空状态
  .no-data.small {
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx 24rpx;
    text-align: center;
    color: #999;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

    .no-data-illustration {
      font-size: 64rpx;
      margin-bottom: 16rpx;
      opacity: 0.6;
    }
    .no-data-title {
      font-size: 24rpx;
      color: #666;
    }
  }
}

@media (max-width: 768px) {
  .waterTankArchives {
    padding: 16rpx;
  }
  .info-grid {
    grid-template-columns: 1fr;
  }
  .tank-details {
    grid-template-columns: 1fr;
  }
}
</style>
