<template>
  <div class="waterTankArchives">
    <div class="info-card water-tank-card" v-if="menuData.T && menuData.T.length">
      <div class="card-header">
        <div class="header-icon">
          <wd-icon name="tools" size="18px" color="#13c2c2"></wd-icon>
        </div>
        <div class="header-title">水箱档案</div>
        <div class="header-extra" v-if="menuData.T && menuData.T.length">
          <span class="extra-count"
            >共 <strong>{{ menuData.total_count || (menuData.T && menuData.T.length) || 0 }}</strong> 个</span
          >
          <span class="extra-chip">区域类别：{{ menuData.area_category || '-' }}</span>
          <span class="extra-chip">系统编码：{{ menuData.system_code || '-' }}</span>
        </div>
      </div>

      <div class="tank-list-compact">
        <div class="tank-row" v-for="(tank, index) in menuData.T" :key="index">
          <div class="row-header">
            <div class="row-badge">{{ index + 1 }}</div>
            <div class="row-title">{{ tank.location || '未填写位置' }}</div>
          </div>
          <div class="row-body">
            <div class="kv">
              <span class="k">容量</span><span class="v">{{ tank.capacity || '-' }}</span>
            </div>
            <div class="kv">
              <span class="k">数量</span><span class="v">{{ tank.quantity || '-' }}</span>
            </div>
            <div class="kv">
              <span class="k">类型</span><span class="v">{{ tank.type || '-' }}</span>
            </div>
            <div class="kv">
              <span class="k">材质</span><span class="v">{{ tank.material || '-' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="!menuData.T || !menuData.T.length" class="no-data small">
      <div class="no-data-illustration">�</div>
      <div class="no-data-title">暂无水箱档案</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({ data: { type: Object } })

const safeSplit = (val) => (val ?? '').toString().split(';')

const menuData = computed(() => {
  const d = props.data
  if (!d) return {}
  const { total_count, area_category, system_code } = d
  const locations = safeSplit(d.location)
  const capacities = safeSplit(d.capacity)
  const types = safeSplit(d.type)
  const materials = safeSplit(d.material)
  const quantities = safeSplit(d.quantity)
  const T = locations.map((item, index) => ({
    location: item || '-',
    capacity: capacities[index] || '-',
    type: types[index] || '-',
    material: materials[index] || '-',
    quantity: quantities[index] || '-'
  }))
  return { T, total_count, area_category, system_code }
})
</script>

<style lang="less" scoped>
.waterTankArchives {
  padding: 12px; // 更紧凑
  background: #f5f5f5;

  .info-card.water-tank-card {
    background: #fff;
    border-radius: 12rpx;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    flex-wrap: wrap;

    .header-icon {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .header-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
    .header-extra {
      margin-left: auto;
      display: flex;
      flex-wrap: wrap;
      gap: 6px 8px;
      align-items: center;
      font-size: 12px;
      color: #666;
      line-height: 1.2;

      .extra-count strong {
        color: #111;
        font-weight: 700;
      }
      .extra-chip {
        background: #f5f7fa;
        color: #666;
        padding: 2px 6px;
        border-radius: 10px;
      }
    }
  }

  // 紧凑行列表
  .tank-list-compact {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  .tank-row {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 8px 10px;
    background: #fff;
    position: relative;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 10px;
      right: 10px;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, #e8e8e8 20%, #e8e8e8 80%, transparent 100%);
    }
  }
  .row-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
  }
  .row-badge {
    min-width: 18px;
    height: 18px;
    border-radius: 9px;
    background: #e6f4ff;
    color: #1677ff;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  .row-title {
    font-size: 13px;
    font-weight: 600;
    color: #222;
    flex: 1;
  }

  .row-body {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 6px 10px;
  }
  .kv {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    align-items: center;
    padding: 2px 0;
    border-bottom: 1px solid #f8f8f8;

    &:last-child {
      border-bottom: none;
    }
  }
  .k {
    font-size: 12px;
    color: #777;
    min-width: 44px;
  }
  .v {
    font-size: 13px;
    color: #333;
    font-weight: 600;
    flex: 1;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 空状态（小号）
  .no-data.small {
    background: #fff;
    border-radius: 8px;
    padding: 16px 12px;
    text-align: center;
    color: #888;
    .no-data-illustration {
      display: none;
    }
    .no-data-title {
      font-size: 13px;
    }
  }
}

@media (max-width: 768px) {
  .waterTankArchives {
    padding: 10px;
  }
  .row-body {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
</style>
