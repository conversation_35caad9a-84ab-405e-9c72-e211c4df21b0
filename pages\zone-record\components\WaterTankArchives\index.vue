<template>
  <div class="waterTankArchives">
    <!-- 统计概览 -->
    <div class="stats">
      <div class="stat-card">
        <div class="stat-icon">🧮</div>
        <div class="stat-meta">
          <div class="label">总数量</div>
          <div class="value">{{ menuData.total_count || 0 }}</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">📍</div>
        <div class="stat-meta">
          <div class="label">区域类别</div>
          <div class="value">{{ menuData.area_category || '-' }}</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🔖</div>
        <div class="stat-meta">
          <div class="label">系统编码</div>
          <div class="value code">{{ menuData.system_code || '-' }}</div>
        </div>
      </div>
    </div>

    <!-- 列表 -->
    <div class="tank-list" v-if="menuData.T && menuData.T.length">
      <div class="list-header">
        <h3 class="section-title">水箱档案</h3>
        <div class="list-meta">共 {{ menuData.T.length }} 条</div>
      </div>

      <div class="tank-grid">
        <div v-for="(tank, index) in menuData.T" :key="index" class="tank-card">
          <div class="card-top">
            <div class="title-row">
              <span class="title">{{ tank.location || '未填写位置' }}</span>
              <span class="badge">#{{ index + 1 }}</span>
            </div>
            <div class="chips">
              <span class="chip type" v-if="tank.type">{{ tank.type }}</span>
              <span class="chip material" v-if="tank.material">{{ tank.material }}</span>
            </div>
          </div>

          <div class="metrics">
            <div class="metric">
              <span class="metric-icon">🫗</span>
              <div class="metric-text">
                <div class="metric-label">容量</div>
                <div class="metric-value">{{ tank.capacity || '-' }}</div>
              </div>
            </div>
            <div class="metric">
              <span class="metric-icon">📦</span>
              <div class="metric-text">
                <div class="metric-label">数量</div>
                <div class="metric-value">{{ tank.quantity || '-' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="no-data modern">
      <div class="no-data-illustration">�</div>
      <div class="no-data-title">暂无水箱档案</div>
      <div class="no-data-sub">请检查筛选条件或稍后重试</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({ data: { type: Object } })

const safeSplit = (val) => (val ?? '').toString().split(';')

const menuData = computed(() => {
  const d = props.data
  if (!d) return {}
  const { total_count, area_category, system_code } = d
  const locations = safeSplit(d.location)
  const capacities = safeSplit(d.capacity)
  const types = safeSplit(d.type)
  const materials = safeSplit(d.material)
  const quantities = safeSplit(d.quantity)
  const T = locations.map((item, index) => ({
    location: item || '-',
    capacity: capacities[index] || '-',
    type: types[index] || '-',
    material: materials[index] || '-',
    quantity: quantities[index] || '-'
  }))
  return { T, total_count, area_category, system_code }
})
</script>

<style lang="less" scoped>
.waterTankArchives {
  padding: 20px;
  background: #f7f8fa;
  border-radius: 10px;

  .stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 20px;

    .stat-card {
      display: flex;
      align-items: center;
      gap: 12px;
      background: #fff;
      padding: 14px 16px;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .stat-icon {
        font-size: 20px;
      }
      .stat-meta {
        .label {
          font-size: 12px;
          color: #86909c;
        }
        .value {
          font-size: 18px;
          font-weight: 600;
          color: #1d2129;
        }
        .value.code {
          font-family: ui-monospace, SFMono-Regular, Menlo, monospace;
          font-size: 14px;
          color: #3b82f6;
        }
      }
    }
  }

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0 12px;
    .section-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
    }
    .list-meta {
      font-size: 12px;
      color: #6b7280;
    }
  }

  .tank-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 14px;

    .tank-card {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
      padding: 14px;
      transition: transform 0.18s ease, box-shadow 0.18s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      }

      .card-top {
        .title-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .title {
          font-size: 15px;
          font-weight: 600;
          color: #111827;
        }
        .badge {
          background: #eff6ff;
          color: #2563eb;
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 999px;
        }
        .chips {
          margin-top: 8px;
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }
        .chip {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 999px;
          background: #f3f4f6;
          color: #4b5563;
        }
        .chip.type {
          background: #ecfeff;
          color: #0891b2;
        }
        .chip.material {
          background: #fef3c7;
          color: #b45309;
        }
      }

      .metrics {
        margin-top: 12px;
        display: grid;
        grid-template-columns: repeat(2, minmax(0, 1fr));
        gap: 10px;
      }
      .metric {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px 12px;
        background: #fafafa;
        border-radius: 10px;
      }
      .metric-icon {
        font-size: 18px;
      }
      .metric-label {
        font-size: 12px;
        color: #6b7280;
      }
      .metric-value {
        font-size: 14px;
        font-weight: 600;
        color: #374151;
      }
    }
  }

  .no-data.modern {
    text-align: center;
    padding: 56px 16px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
    .no-data-illustration {
      font-size: 52px;
      opacity: 0.7;
    }
    .no-data-title {
      margin-top: 10px;
      font-size: 16px;
      color: #374151;
      font-weight: 600;
    }
    .no-data-sub {
      margin-top: 4px;
      font-size: 12px;
      color: #9ca3af;
    }
  }
}

@media (max-width: 768px) {
  .waterTankArchives {
    padding: 16px;
  }
  .tank-grid {
    grid-template-columns: 1fr !important;
  }
}
</style>
